import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:screen_capture_event/screen_capture_event.dart';
import 'package:get/get.dart';

class CaptureUtil {
  static ScreenCaptureEvent _screenCaptureEvent = ScreenCaptureEvent();
  static bool _isInitialized = false;

  /// 录屏状态 - 响应式变量
  static RxBool isRecording = false.obs;

  /// 截屏路径 - 响应式变量
  static RxString screenshotPath = ''.obs;

  /// 初始化录屏监听
  static void initScreenCaptureListener() {
    if (_isInitialized) return;

    logger.d('初始化录屏监听器');

    // 添加录屏监听器
    _screenCaptureEvent.addScreenRecordListener((recorded) {
      logger.d('录屏状态变化: $recorded');
      isRecording.value = recorded;
    });

    // 添加截屏监听器
    _screenCaptureEvent.addScreenShotListener((screenshot) {
      logger.d('截屏检测: $screenshot');
      screenshotPath.value = screenshot;
    });

    // 开始监听
    _screenCaptureEvent.watch();
    _isInitialized = true;
  }

  /// 检查录屏 - 已废弃，请使用 isRecording 响应式变量
  @Deprecated('使用 CaptureUtil.isRecording 响应式变量代替')
  static bool checkRecord() {
    initScreenCaptureListener();
    return isRecording.value;
  }

  /// 截屏检测 - 已废弃，请使用 screenshotPath 响应式变量
  @Deprecated('使用 CaptureUtil.screenshotPath 响应式变量代替')
  static String checkScreenshot() {
    initScreenCaptureListener();
    return screenshotPath.value;
  }

  /// 销毁监听器
  static void dispose() {
    logger.d('销毁录屏监听器');
    _screenCaptureEvent.dispose();
    _isInitialized = false;
    isRecording.value = false;
    screenshotPath.value = '';
  }
}
