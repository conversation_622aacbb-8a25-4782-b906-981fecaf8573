import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:screen_capture_event/screen_capture_event.dart';

class CaptureUtil {
  static ScreenCaptureEvent _screenCaptureEvent = ScreenCaptureEvent();

  /// 检查录屏
  static bool checkRecord() {
    bool isRecording = false;
    logger.d('录屏状态: $isRecording');

    _screenCaptureEvent.addScreenRecordListener((recorded) {
      logger.d('录屏状态: $recorded');
      isRecording = recorded;
    });

    _screenCaptureEvent.watch();

    return isRecording;
  }

  /// 截屏检测
  static String checkScreenshot() {
    String path = '';
    logger.d('截屏状态: $path');

    _screenCaptureEvent.addScreenShotListener((screenshot) {
      logger.d('截屏状态: $screenshot');
      path = screenshot;
    });

    _screenCaptureEvent.watch();

    return path;
  }

  static dispose() => _screenCaptureEvent.dispose();
}
