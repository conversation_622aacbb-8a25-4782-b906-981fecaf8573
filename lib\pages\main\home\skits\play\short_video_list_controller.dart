import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/req/skits/req_skits.dart';
import 'package:flutter_starry_sky_box/api/req/user/req_user.dart';
import 'package:flutter_starry_sky_box/model/skits/get_skits_model.dart';
import 'package:flutter_starry_sky_box/model/user/see_top_tasks_model.dart';
import 'package:flutter_starry_sky_box/pages/main/my/user_store.dart';
import 'package:flutter_starry_sky_box/routers/app_routes.dart';
import 'package:flutter_starry_sky_box/utils/capture_util.dart';
import 'package:flutter_starry_sky_box/utils/custom_constant.dart';
import 'package:flutter_starry_sky_box/utils/custom_event_util.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:flutter_starry_sky_box/widget/custom_preload_page_view.dart';
import 'package:flutter_starry_sky_box/widget/dialog/pick_up_beans_dialog.dart';
import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
import 'package:get/get.dart';
import 'package:screen_capture_event/screen_capture_event.dart';

import '../../../../../widget/svga_widget.dart';

class ShortVideoListController extends GetxController
    with GetTickerProviderStateMixin, WidgetsBindingObserver {
  static ShortVideoListController get to => Get.find();

  TabController? tabController;

  PreloadPageController? preloadPageController;
  final String type;
  final String skitsid;
  ShortVideoListController({this.skitsid = '0', this.type = '0'});

  SeeTopTasksModel? seeTopTasksModel;

  ScreenCaptureEvent screenCaptureEvent = ScreenCaptureEvent();

  /// 是否录屏
  var isRecording = false;

  @override
  void onInit() {
    super.onInit();

    tabController = TabController(length: 2, vsync: this, initialIndex: 0);

    getUserSeeTopTasks();
    skitsGetSkits();
    _checkRecord();

    eventBus.on<BottomNavigationBarEvent>().listen((event) {
      if (event.message == CustomConstant.SKIT_DJ_REFRESH) {
        GetSkitsModel evData = event.data;
        if (getSkitsModel!.id == evData.id) {
          getSkitsModel?.iscollections = evData.iscollections;
          getSkitsModel?.collections = evData.collections;
        }
        update();
      } else if (event.message == CustomConstant.SKIT_ANTHOLOGY) {
        anthology(event.data);
      }
    });
  }

  _checkRecord() {
    logger.d('录屏状态: $isRecording');

    screenCaptureEvent.addScreenRecordListener((recorded) {
      logger.d('录屏状态: $recorded');
      isRecording = recorded;
    });

    screenCaptureEvent.watch();
  }

  //每次播放--变更剧集都发送eventbus
  void sendFireEvent() {
    eventBus.fire(
        BottomNavigationBarEvent(CustomConstant.EVENT_SHORT_VIDEO_LOOK, data: {
      'skitsid': '${skitsid}',
      'image': '${getSkitsModel?.thumb}',
      'title': '${getSkitsModel?.title}',
      'num': '${getSkitsModel?.nowEpisodes}',
      'type': '${type}'
    }));
  }

  Timer? gkdjsTimer;
  int currentMaxWatchtime = 0;

  getUserSeeTopTasks() async {
    var resp = await ReqUser.userSeeTopTasks();
    if (resp.status == Status.completed) {
      if (resp.data['data']['code'] == 0) {
        if (resp.data['data']['info'].length > 0) {
          seeTopTasksModel =
              SeeTopTasksModel.fromJson(resp.data['data']['info'][0]);

          currentMaxWatchtime = (int.parse(
                          '${seeTopTasksModel?.type1?.goodsTaskTime ?? 0}') -
                      int.parse(
                          '${seeTopTasksModel?.type1?.todayWatchtime ?? 0}')) <=
                  0
              ? 0
              : (int.parse('${seeTopTasksModel?.type1?.goodsTaskTime ?? 0}') -
                  int.parse('${seeTopTasksModel?.type1?.todayWatchtime ?? 0}'));
          update();
        }
      }
    }
  }

  GetSkitsModel? getSkitsModel;
  // 是否最后一页数据
  bool isLastData = false;
  bool isLoading = true;

  skitsGetSkits() async {
    var resp = await ReqSkits.skitsGetSkits(int.parse('$skitsid'));
    if (resp.status == Status.completed) {
      if (resp.data['data']['code'] == 0) {
        if (resp.data['data']['info'].length > 0) {
          getSkitsModel = GetSkitsModel.fromJson(resp.data['data']['info'][0]);
          preloadPageController = PreloadPageController(
              initialPage: getSkitsModel?.nowEpisodes == -1
                  ? 0
                  : (getSkitsModel?.nowEpisodes ?? 0) - 1);

          if (getSkitsModel?.nowEpisodes == -1) {
            getSkitsModel?.nowEpisodes = 0;
          }

          skitsAddView(getSkitsModel?.nowEpisodes == 0
              ? 1
              : getSkitsModel?.nowEpisodes ?? 1);
        }

        isLoading = false;
        update();
      }
    }
  }

  /// 取消\收藏订阅短剧
  skitsAddCollection() async {
    try {
      // EasyLoading.show(status: '${'in_operation'.tr}...');

      bool isCollection = getSkitsModel?.iscollections == '1' ? true : false;

      getSkitsModel?.iscollections = isCollection ? '0' : '1';
      getSkitsModel?.collections = isCollection
          ? (getSkitsModel?.collections ?? 0) - 1
          : (getSkitsModel?.collections ?? 0) + 1;
      update();
      CustomToast.showTextToast('operation_successful'.tr);

      var resp = await ReqSkits.skitsAddCollection(getSkitsModel?.id ?? 0);

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0) {
          // getSkitsModel?.iscollections = resp.data['data']['info'][0]['iscollection'].toString();
          // getSkitsModel?.collections = resp.data['data']['info'][0]['collections'];
          // update();
          // CustomToast.showTextToast('operation_successful'.tr);

          eventBus
              .fire(const BottomNavigationBarEvent(CustomConstant.SKIT_ZUIJU));
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
    }
  }

  /// 取消\点赞短剧
  skitsAddLike() async {
    try {
      // EasyLoading.show(status: '${'in_operation'.tr}...');

      bool isLike = getSkitsModel?.islikes == '1' ? true : false;
      getSkitsModel?.islikes = isLike ? '0' : '1';
      getSkitsModel?.likes = isLike
          ? (getSkitsModel?.likes ?? 0) - 1
          : (getSkitsModel?.likes ?? 0) + 1;
      update();
      CustomToast.showTextToast('operation_successful'.tr);

      var resp = await ReqSkits.skitsAddLike(getSkitsModel?.id ?? 0);

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0) {
          // getSkitsModel?.islikes =
          //     resp.data['data']['info'][0]['islike'].toString();
          // getSkitsModel?.likes = resp.data['data']['info'][0]['likes'];
          // update();
          // CustomToast.showTextToast('operation_successful'.tr);

          eventBus.fire(
              const BottomNavigationBarEvent(CustomConstant.SKIT_DIANZAN));
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
    }
  }

  int position = 0;

  // 切换播放第几集
  skitsAddView(int num) async {
    getSkitsModel?.nowEpisodes = num;
    var resp =
        await ReqSkits.skitsAddView(skitsid: getSkitsModel?.id, num: num);

    if (resp.status == Status.completed) {
      if (resp.data['data']['code'] == 0) {
        getSkitsModel?.nowEpisodes = num;
        update();
        sendFireEvent();
        eventBus
            .fire(const BottomNavigationBarEvent(CustomConstant.SKIT_LISHI));
      }
    }

    if (getSkitsModel?.episodeslist?.length == 0) {
      CustomToast.showTextToast(
          'no_videos_have_been_added_for_the_current_skit_please_add_them'.tr);
    }
  }

  // 选集
  anthology(int num) async {
    getSkitsModel?.nowEpisodes = num;
    preloadPageController?.jumpToPage(num - 1);
    sendFireEvent();
  }

  // 开始观看视频
  startWatchVideo() async {
    updateWatchtime();
  }

  // 结束观看视频
  endWatchVideo() async {
    wscTimer?.cancel();
    gkdjsTimer?.cancel();

    int tempWsc = wsc;
    wsc = 0;
    Map<String, dynamic> map = HashMap();
    map['watchtime'] = tempWsc;
    if (tempWsc == 0) {
      return;
    }
    var resp = await ReqUser.updateFields(json.encode(map));
    if (resp.status == Status.completed) {
      if (resp.data['data']['code'] == 0) {
        logger.d('---------结束：修改用户观看短剧视频时长');
        getUserSeeTopTasks();
      }
    }
  }

  Timer? wscTimer;
  int wsc = 0;

  @override
  void onClose() {
    wscTimer?.cancel();
    gkdjsTimer?.cancel();
    super.onClose();
  }

  // 修改用户观看视频时长
  updateWatchtime() async {
    wscTimer?.cancel();
    wscTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      wsc++;
      if (wsc >= 10) {
        int tempWsc = wsc;
        wsc = 0;
        Map<String, dynamic> map = HashMap();
        map['watchtime'] = tempWsc;
        if (tempWsc == 0) {
          return;
        }
        var resp = await ReqUser.updateFields(json.encode(map));
        if (resp.status == Status.completed) {
          if (resp.data['data']['code'] == 0) {
            logger.d('---------修改用户观看短剧视频时长');
            getUserSeeTopTasks();
          }
        }
      }
    });

    gkdjsTimer?.cancel();
    gkdjsTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      currentMaxWatchtime--;
      if (currentMaxWatchtime <= 0) {
        currentMaxWatchtime = 0;

        if (seeTopTasksModel?.type1?.isBuyGoods == '1' &&
            seeTopTasksModel?.type1?.isReceive == '0' &&
            Get.currentRoute.startsWith(AppRoutes.shortVideoList)) {
          if (double.parse('${seeTopTasksModel?.type1?.todayWatchtime ?? 0}') >=
                  double.parse(
                      '${seeTopTasksModel?.type1?.goodsTaskTime ?? 0}') &&
              isShowLq == false) {
            WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
              isShowLq = true;
              PickUpBeansDialog.show(Get.context!,
                  money: '${seeTopTasksModel?.type1?.goodsTaskReward ?? 0}',
                  confirmTap: () {
                receiveTopTaskReward();
              });
            });
          }
        }
      }

      update();
    });
  }

  bool isShowLq = false;

  /// 领取高级奖励
  receiveTopTaskReward() async {
    try {
      EasyLoading.show(status: '${'in_operation'.tr}...');

      var resp = await ReqUser.receiveTopTaskReward();

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0) {
          CustomToast.showTextToast('operation_successful'.tr);

          // Get.back();
          seeTopTasksModel?.type1?.isReceive = '1';

          // 领取高级奖励动画
          showSvgaDialog('assets/svga/check_in_success.svga');
          update();
          eventBus.fire(
              const BottomNavigationBarEvent(CustomConstant.SKIT_RECEIVE));

          UserStore.to.getBaseInfo();
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
    }
  }
}
